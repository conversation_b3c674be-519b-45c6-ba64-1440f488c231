<!--推送年度考核企业或设计划弹窗-->
<template>
  <BaseDialog
    title="推送年度考核企业或设计划"
    :visible.sync="dialogVisible"
    type="edit"
    size="Small"
    :close-on-click-modal="false"
    @confirm="handleConfirm"
    @cancel="handleCancel"
  >
    <el-form ref="rectificationForm" :model="formData" label-width="100px" class="rectification-form">
      <el-form-item label="领导审批" prop="leaderApproval">
        <el-select
          v-model="formData.leaderApproval"
          placeholder="请选择"
          style="width: 100%"
        >
          <el-option label="同意" value="同意"></el-option>
          <el-option label="不同意" value="不同意"></el-option>
        </el-select>
      </el-form-item>

      <el-form-item label="领导审批意见" prop="leaderOpinion">
        <el-input
          v-model="formData.leaderOpinion"
          type="textarea"
          :rows="5"
          placeholder="请输入领导审批意见"
          maxlength="300"
          show-word-limit
          resize="none"
        />
      </el-form-item>
    </el-form>
  </BaseDialog>
</template>

<script>
import BaseDialog from '@/components/BaseDialog/index.vue'

export default {
  name: "rectificationPlan",
  components: {
    BaseDialog
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    // 初始数据
    initialData: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      formData: {
        leaderApproval: '',
        leaderOpinion: ''
      }
    }
  },
  computed: {
    dialogVisible: {
      get() {
        return this.visible
      },
      set(val) {
        this.$emit('update:visible', val)
      }
    }
  },
  watch: {
    visible(newVal) {
      if (newVal) {
        this.initFormData()
      }
    }
  },
  methods: {
    // 初始化表单数据
    initFormData() {
      this.formData = {
        leaderApproval: this.initialData.leaderApproval || '',
        leaderOpinion: this.initialData.leaderOpinion || ''
      }
    },

    // 确认提交
    handleConfirm() {
      this.$refs.rectificationForm.validate((valid) => {
        if (valid) {
          // 验证必填项
          if (!this.formData.leaderApproval) {
            this.$message.warning('请选择领导审批结果')
            return
          }

          this.$emit('confirm', { ...this.formData })
          this.dialogVisible = false
        }
      })
    },

    // 取消
    handleCancel() {
      this.dialogVisible = false
    }
  }
}
</script>

<style scoped>
.rectification-form {
  padding: 20px 0;
}

.rectification-form .el-form-item {
  margin-bottom: 20px;
}

.rectification-form .el-form-item:last-child {
  margin-bottom: 0;
}

.rectification-form .el-textarea__inner {
  font-family: inherit;
  line-height: 1.5;
}

.rectification-form .el-select {
  width: 100%;
}

/* 下拉选择样式 */
.rectification-form .el-select .el-input__inner {
  height: 36px;
  line-height: 36px;
}
</style>
